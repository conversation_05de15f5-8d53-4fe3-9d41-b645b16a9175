import { ChangeDetectorRef, Component, EventEmitter, Output, OnInit } from '@angular/core';
import { PropertyService } from 'src/app/pages/broker/services/property.service';
import { TranslationService } from 'src/app/modules/i18n';

@Component({
  selector: 'app-model-filter',
  templateUrl: './model-filter.component.html',
  styleUrl: './model-filter.component.scss'
})
export class ModelFilterComponent implements OnInit {

  unitTypes: { key: string; value: string }[] = [];

  @Output() filtersApplied = new EventEmitter<any>();

  filter = {
    noOfRooms: '',
    noOfBathrooms: '',
  };

  constructor(
    private propertyService: PropertyService,
    private cdr: ChangeDetectorRef,
    public translationService: TranslationService
  ) {}

  ngOnInit(): void {}

  apply() {
    this.filtersApplied.emit(this.filter);
  }

  // Helper method to get translated text
  getTranslatedText(key: string): string {
    const translations: any = {
      'ar': {
        'NO_OF_ROOMS': 'عدد الغرف',
        'NO_OF_BATHROOMS': 'عدد الحمامات',
        'ENTER_NUMBER_OF_ROOMS': 'أدخل عدد الغرف',
        'ENTER_NUMBER_OF_BATHROOMS': 'أدخل عدد الحمامات',
        'APPLY': 'تطبيق'
      },
      'en': {
        'NO_OF_ROOMS': 'No of Rooms',
        'NO_OF_BATHROOMS': 'No of Bathrooms',
        'ENTER_NUMBER_OF_ROOMS': 'Enter number of rooms',
        'ENTER_NUMBER_OF_BATHROOMS': 'Enter number of bathrooms',
        'APPLY': 'Apply'
      }
    };

    const currentLang = this.translationService.getCurrentLanguage();
    return translations[currentLang]?.[key] || key;
  }

}
