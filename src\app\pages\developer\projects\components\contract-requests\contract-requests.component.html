<div class="mb-5 mt-0">
  <app-developer-header [title]="getTranslatedText('CONTRACT_REQUESTS')"
    [subtitle]="getTranslatedText('VIEW_AND_MANAGE_CONTRACT_REQUESTS')">
  </app-developer-header>
</div>

<div class="card">
  <div class="card-body pt-0">
    <div class="table-responsive">
      <table class="table table-row-bordered table-row-gray-100 align-middle gs-0 gy-3 mt-5">
        <thead>
          <tr class="fw-bold bg-light-dark-blue text-dark-blue me-1 ms-1"
            [class.rtl-table-header]="translationService.getCurrentLanguage() === 'ar'">
            <th class="w-25px ps-4 rounded-start">
              <div class="form-check form-check-sm form-check-custom form-check-solid">
                <input class="form-check-input" type="checkbox" value="1" data-kt-check="true"
                  data-kt-check-target=".widget-13-check" />
              </div>
            </th>
            <th class="min-w-150px cursor-pointer" (click)="sortData('broker_id')">
              {{ getTranslatedText('BROKER_NAME') }}
              <span class="ms-1 text-primary fw-bold">{{ getSortArrow('broker_id') }}</span>
            </th>
            <th class="min-w-150px">
              {{ getTranslatedText('CONTRACT_IMAGE') }}
            </th>
            <th class="min-w-120px cursor-pointer" (click)="sortData('created_at')">
              {{ getTranslatedText('REQUEST_DATE') }}
              <span class="ms-1 text-primary fw-bold">{{ getSortArrow('created_at') }}</span>
            </th>
            <th class="min-w-100px cursor-pointer" (click)="sortData('status')">
              {{ getTranslatedText('STATUS') }}
              <span class="ms-1 text-primary fw-bold">{{ getSortArrow('status') }}</span>
            </th>
            <th class="min-w-120px">
              {{ getTranslatedText('CONTACT_INFO') }}
            </th>
            <th class="text-end min-w-100px pe-4 rounded-end">{{ getTranslatedText('ACTIONS') }}</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let request of rows; index as i" class="odd">
            <td class="ps-4">
              <div class="form-check form-check-sm form-check-custom form-check-solid">
                <input class="form-check-input widget-13-check" type="checkbox" value="1" />
              </div>
            </td>
            <td>
              <div class="d-flex align-items-center">
                <div class="symbol symbol-45px me-5">
                  <img [src]="
                      request.broker.image || 'assets/media/avatars/300-1.jpg'
                    " alt="Broker Image" class="rounded-circle" />
                </div>
                <div class="d-flex justify-content-start flex-column">
                  <span class="text-dark fw-bold text-hover-primary fs-6">
                    {{ request.broker.fullName || "N/A" }}
                  </span>
                  <span class="text-muted fw-semibold text-muted d-block fs-7">
                    {{ getTranslatedText('ID') }}: {{ request.broker.id || "N/A" }}
                  </span>
                </div>
              </div>
            </td>
            <td>
              <button class="btn btn-link text-dark fw-bold fs-6 p-0 text-start"
                (click)="openCompanyModal(companyModal, request)"
                style="text-decoration: none; border: none; background: none">
                {{ getTranslatedText('CONTRACT_IMAGES') }}
                <i class="fa-solid fa-images text-primary"
                  [class.ms-2]="translationService.getCurrentLanguage() !== 'ar'"
                  [class.me-2]="translationService.getCurrentLanguage() === 'ar'"></i>
              </button>
            </td>
            <td>
              <span class="text-dark fw-bold d-block fs-6">
                {{ request.contract.createAt | date : "dd MMM yyyy" }}
              </span>
            </td>
            <td>
              <span class="badge fs-7 fw-bold" [ngClass]="
                  getStatusBadgeClass(
                    request.contract?.status || request.status
                  )
                ">
                {{ request.contract?.status || request.status || "Pending" }}
              </span>
            </td>
            <td>
              <div class="d-flex flex-column">
                <span class="text-dark fw-bold fs-7" *ngIf="request.broker.email">
                  <i class="fa-solid fa-envelope me-1"></i>
                  {{ request.broker.email }}
                </span>
                <span class="text-dark fw-bold fs-7" *ngIf="request.broker.phone">
                  <i class="fa-solid fa-phone me-1"></i>
                  {{ request.broker.phone }}
                </span>
              </div>
            </td>
            <td class="text-end pe-4">
              <div class="dropdown">
                <button type="button" class="btn btn-sm btn-icon btn-color-primary btn-active-light-primary"
                  data-bs-toggle="dropdown" aria-expanded="false">
                  <i class="fa-solid fa-ellipsis-vertical"></i>
                </button>
                <app-contract-requests-dropdown-action-menu
                  [id]="request.contract?.id || request.id || request.requestId"
                  (actionCompleted)="reloadTable(page)"></app-contract-requests-dropdown-action-menu>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Loading State -->
    <div *ngIf="loading" class="text-center py-5">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">{{ getTranslatedText('LOADING') }}</span>
      </div>
    </div>

    <!-- Empty State -->
    <div *ngIf="!loading && rows.length === 0" class="text-center py-5"
      [class.rtl-empty-state]="translationService.getCurrentLanguage() === 'ar'">
      <div class="text-muted fs-4">
        <div class="mb-3">
          <i class="fa-solid fa-file-contract fs-1 text-muted"
            *ngIf="translationService.getCurrentLanguage() !== 'ar'"></i>
          <i class="fa-solid fa-file-text fs-1 text-muted" *ngIf="translationService.getCurrentLanguage() === 'ar'"></i>
        </div>
        <p>{{ getTranslatedText('NO_CONTRACT_REQUESTS_FOUND') }}</p>
      </div>
    </div>

    <!-- Pagination -->
    <div *ngIf="!loading && rows.length > 0" class="d-flex justify-content-center mt-5">
      <app-pagination [totalItems]="page.totalElements" [itemsPerPage]="page.size" [currentPage]="page.pageNumber"
        (pageChange)="onPageChange($event)"></app-pagination>
    </div>
  </div>
</div>

<!-- Company Images Modal -->
<ng-template #companyModal let-modal>
  <div class="modal-header">
    <h4 class="modal-title">Images</h4>
    <button type="button" class="btn-close" (click)="modal.dismiss()"></button>
  </div>

  <div class="modal-body position-relative">
    <div class="text-center">
      <!-- No images message -->
      <div *ngIf="!hasImages()" class="no-images-container d-flex flex-column align-items-center justify-content-center"
        style="min-height: 300px">
        <i class="fa-solid fa-image fs-1 text-muted mb-3"></i>
        <h5 class="text-muted">No images available</h5>
        <p class="text-muted">There are no images for this request.</p>
      </div>

      <!-- Images display -->
      <div *ngIf="hasImages()">
        <!-- Navigation buttons -->
        <button *ngIf="hasMultipleImages()"
          class="btn btn-icon btn-light-primary position-absolute top-50 start-0 translate-middle-y rounded-circle"
          (click)="prevImage()" style="z-index: 10">
          <i class="bi bi-chevron-left fs-2"></i>
        </button>

        <!-- Image display -->
        <div class="image-container">
          <img [src]="getCurrentImage().url" class="img-fluid rounded"
            style="max-height: 70vh; max-width: 100%; object-fit: contain" [alt]="selectedCompanyName + ' image'" />
        </div>

        <button *ngIf="hasMultipleImages()"
          class="btn btn-icon btn-light-primary position-absolute top-50 end-0 translate-middle-y rounded-circle"
          (click)="nextImage()" style="z-index: 10">
          <i class="bi bi-chevron-right fs-2"></i>
        </button>
      </div>
    </div>
  </div>

  <!-- Modal footer with navigation dots -->
  <div *ngIf="hasMultipleImages()" class="modal-footer justify-content-center">
    <div class="d-flex gap-2">
      <button *ngFor="let image of selectedImages; let i = index" class="btn btn-sm rounded-circle p-1"
        [class.btn-primary]="i === currentImageIndex" [class.btn-light]="i !== currentImageIndex"
        (click)="currentImageIndex = i" style="width: 12px; height: 12px"></button>
    </div>
  </div>
</ng-template>
