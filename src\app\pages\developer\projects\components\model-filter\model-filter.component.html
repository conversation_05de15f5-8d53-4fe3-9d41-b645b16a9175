<div class="filter-dropdown" [class.rtl-filter]="translationService.getCurrentLanguage() === 'ar'">
  <div class="mb-2">
    <label class="form-label">{{ getTranslatedText('NO_OF_ROOMS') }}:</label>
    <input type="number" class="form-control form-control-sm" [(ngModel)]="filter.noOfRooms" min="1" step="1"
      [placeholder]="getTranslatedText('ENTER_NUMBER_OF_ROOMS')" />
  </div>

  <div class="mb-2">
    <label class="form-label">{{ getTranslatedText('NO_OF_BATHROOMS') }}:</label>
    <input type="number" class="form-control form-control-sm" [(ngModel)]="filter.noOfBathrooms" min="1" step="1"
      [placeholder]="getTranslatedText('ENTER_NUMBER_OF_BATHROOMS')" />
  </div>

  <button class="btn btn-sm btn-primary w-100" (click)="apply()">{{ getTranslatedText('APPLY') }}</button>
</div>
