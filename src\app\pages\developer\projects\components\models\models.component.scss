// RTL Support for Models Component
.rtl-layout {
  direction: rtl;
  text-align: right;

  h1 {
    font-family: 'Hacen Liner Screen St', sans-serif;
    font-weight: bold;
  }

  .btn {
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  .form-control {
    text-align: right;
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  .table {
    th, td {
      text-align: right;
      font-family: 'Hacen Liner Screen St', sans-serif;
    }
  }
}

.rtl-upload {
  direction: rtl;

  .btn {
    font-family: 'Hacen Liner Screen St', sans-serif;
  }
}

:host-context(html[lang="ar"]) {
  .card-body {
    direction: rtl;
    padding: 2rem 1.5rem !important;
  }

  .d-flex.justify-content-between {
    gap: 1rem;
  }

  .btn {
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  .form-control {
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  h1 {
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  .table {
    direction: rtl;

    th, td {
      text-align: right;
      font-family: 'Hacen Liner Screen St', sans-serif;
    }
  }
}
