import { Component, OnInit, HostListener, <PERSON><PERSON>iew<PERSON><PERSON>t, <PERSON><PERSON><PERSON>roy, ChangeDetectorRef } from '@angular/core';
import { Router } from '@angular/router';
import { AuthenticationService } from '../authentication/services/authentication.service';
import { HomeService } from './services/home.service';
import { TranslationService } from '../../modules/i18n/translation.service';
import { TranslateService } from '@ngx-translate/core';
import { PropertyTranslationService } from '../../shared/services/property-translation.service';
import Swal from 'sweetalert2';

declare var bootstrap: any;

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss']
})
export class HomeComponent implements OnInit, AfterViewInit, OnDestroy {
  currentUser: any = null;
  isLoggedIn: boolean = false;
  showUserDropdown: boolean = false;
  showMobileMenu: boolean = false;
  properties: any[] = [];

  // Location Carousel Data
  locationSlides: any[][] = [];
  currentSlideIndex: number = 0;
  carouselInterval: any;

  // Articles Carousel Data
  currentArticleSlideIndex: number = 0;
  articlesCarouselInterval: any;
  locations: any[] = [];
  currentLang: string = 'en';

  constructor(
    private homeService: HomeService,
    private cd: ChangeDetectorRef,
    private router: Router,
    private translationService: TranslationService,
    private translateService: TranslateService,
    private propertyTranslationService: PropertyTranslationService
  ) { }

  ngOnInit(): void {
    this.checkUserSession();
    this.lodingProperties();
    this.loadingExploreLocations();

    // Initialize translations
    this.initializeTranslations();

    // Get current language
    this.currentLang = this.translationService.getCurrentLanguage();

    // Subscribe to language changes
    this.translationService.currentLanguage$.subscribe(lang => {
      this.currentLang = lang;
      this.cd.detectChanges();
    });
  }

  initializeTranslations(): void {
    // Get current language
    const currentLang = this.translationService.getCurrentLanguage();
    console.log('Current language:', currentLang);

    // Set the language in TranslateService with delay
    setTimeout(() => {
      this.translateService.use(currentLang);

      // Apply direction
      this.translationService.setLanguage(currentLang);

      // Test translation
      this.translateService.get('HOME.ARTICLES.ARTICLE_1.TITLE').subscribe(translation => {
        console.log('Translation test:', translation);
      });

      // Force change detection
      this.cd.detectChanges();
    }, 100);
  }

  ngAfterViewInit(): void {
    // Initialize Bootstrap carousels after view is loaded
    setTimeout(() => {
      this.initializeCarousel();
      this.initializeArticlesCarousel();
    }, 100);
  }

  ngOnDestroy(): void {
    // Clean up intervals when component is destroyed
    if (this.carouselInterval) {
      clearInterval(this.carouselInterval);
    }
    if (this.articlesCarouselInterval) {
      clearInterval(this.articlesCarouselInterval);
    }
  }

  checkUserSession(): void {
    // Check if user is logged in by checking localStorage
    const authToken = localStorage.getItem('authToken');
    const currentUser = localStorage.getItem('currentUser');

    if (authToken && currentUser) {
      try {
        this.currentUser = JSON.parse(currentUser);
        this.isLoggedIn = true;
      } catch (error) {
        // If parsing fails, user is not logged in
        this.isLoggedIn = false;
        this.currentUser = null;
      }
    } else {
      this.isLoggedIn = false;
      this.currentUser = null;
    }
  }

  getUserDisplayName(): string {
    if (this.currentUser) {
      return this.currentUser.fullName  || 'User';
    }
    return 'Guest';
  }

  getUserProfileImage(): string {
    if (this.currentUser && this.currentUser.image) {
      return this.currentUser.image;
    }
    // Return default avatar if no profile image
    return 'assets/media/avatars/blank.png';
  }

  // Get user role
  getUserRole(): string {
    if (this.currentUser && this.currentUser.role) {
      return this.currentUser.role.toLowerCase();
    }
    return '';
  }

  // Check if user is admin
  isAdmin(): boolean {
    return this.getUserRole() === 'admin';
  }

  // Check if user is client
  isClient(): boolean {
    return this.getUserRole() === 'client';
  }

  // Check if user is developer
  isDeveloper(): boolean {
    return this.getUserRole() === 'developer';
  }

  // Check if user is broker
  isBroker(): boolean {
    return this.getUserRole() === 'broker';
  }


  lodingProperties() {
  this.homeService.getFeaturedProperties().subscribe({
    next: (response: any) => {
      console.log("sssssssssssssss",response);
      this.properties = response.data || [];
      this.cd.detectChanges();
    },
     error: (error) => {
      console.error('Error loading properties:', error);
      this.properties = [];
      this.cd.detectChanges();
    },
  });
}

loadingExploreLocations() {
  this.homeService.getExploreLocations().subscribe({
    next: (response: any) => {
      console.log("qqqqqqqqqqqqqqq",response);
      this.locations = response.data || [];
      // Re-initialize location slides with API data
      this.initializeLocationSlides();
      this.cd.detectChanges();
    },
     error: (error) => {
      console.error('Error loading locations:', error);
      this.locations = [];
      this.cd.detectChanges();
    },
  });
}

  toggleUserDropdown(): void {
    this.showUserDropdown = !this.showUserDropdown;
  }

  closeUserDropdown(): void {
    this.showUserDropdown = false;
  }

  logout(): void {
    localStorage.removeItem('authToken');
    localStorage.removeItem('currentUser');
    this.isLoggedIn = false;
    this.currentUser = null;
    this.showUserDropdown = false;
    // Optionally redirect to login page
    this.router.navigate(['/authentication/login']);
  }

  toggleMobileMenu(): void {
    this.showMobileMenu = !this.showMobileMenu;
    // Close user dropdown when mobile menu is toggled
    if (this.showMobileMenu) {
      this.showUserDropdown = false;
    }
  }

  closeMobileMenu(): void {
    this.showMobileMenu = false;
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    const target = event.target as HTMLElement;
    const userProfile = target.closest('.user-profile');
    const userDropdown = target.closest('.user-dropdown');
    const navbarToggler = target.closest('.navbar-toggler');
    const mobileNavDropdown = target.closest('.mobile-nav-dropdown');

    // Close user dropdown if clicked outside of user profile and dropdown
    if (!userProfile && !userDropdown && this.showUserDropdown) {
      this.showUserDropdown = false;
    }

    // Close mobile menu if clicked outside of navbar toggler and mobile nav dropdown
    if (!navbarToggler && !mobileNavDropdown && this.showMobileMenu) {
      this.showMobileMenu = false;
    }
  }

  // Location Carousel Methods
  initializeLocationSlides(): void {
    // Split locations into slides of 5 items each
    const itemsPerSlide = 5;
    this.locationSlides = [];

    for (let i = 0; i < this.locations.length; i += itemsPerSlide) {
      this.locationSlides.push(this.locations.slice(i, i + itemsPerSlide));
    }
  }

  initializeCarousel(): void {
    try {
      const carouselElement = document.getElementById('horizontalCarousel');
      if (carouselElement) {
        // Try Bootstrap first
        if (typeof bootstrap !== 'undefined') {
          const carousel = new bootstrap.Carousel(carouselElement, {
            interval: 5000,
            ride: 'carousel',
            wrap: true,
            keyboard: true,
            pause: 'hover'
          });
          console.log('Bootstrap carousel initialized');
        } else {
          // Fallback: Manual carousel control
          this.startManualCarousel();
          console.log('Manual carousel initialized');
        }
      }
    } catch (error) {
      console.error('Error initializing carousel:', error);
      // Fallback to manual carousel
      this.startManualCarousel();
    }
  }

  startManualCarousel(): void {
    // Clear any existing interval
    if (this.carouselInterval) {
      clearInterval(this.carouselInterval);
    }

    // Start auto-play
    this.carouselInterval = setInterval(() => {
      this.nextSlide();
    }, 5000);
  }

  nextSlide(): void {
    const totalSlides = this.locationSlides.length;
    if (totalSlides > 0) {
      this.currentSlideIndex = (this.currentSlideIndex + 1) % totalSlides;
      this.updateCarouselDisplay();
    }
  }

  prevSlide(): void {
    const totalSlides = this.locationSlides.length;
    if (totalSlides > 0) {
      this.currentSlideIndex = this.currentSlideIndex === 0 ? totalSlides - 1 : this.currentSlideIndex - 1;
      this.updateCarouselDisplay();
    }
  }

  updateCarouselDisplay(): void {
    const carouselItems = document.querySelectorAll('#horizontalCarousel .carousel-item');
    carouselItems.forEach((item, index) => {
      if (index === this.currentSlideIndex) {
        item.classList.add('active');
      } else {
        item.classList.remove('active');
      }
    });
  }



  loadMoreProperties(){

    this.router.navigate(['/home/<USER>']);

  }

  scrollToTop(): void {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

  // Articles Carousel Methods
  initializeArticlesCarousel(): void {
    try {
      const carouselElement = document.getElementById('articlesCarousel');
      if (carouselElement) {
        // Try Bootstrap first
        if (typeof bootstrap !== 'undefined') {
          const carousel = new bootstrap.Carousel(carouselElement, {
            interval: 6000,
            ride: 'carousel',
            wrap: true,
            keyboard: true,
            pause: 'hover'
          });
          console.log('Bootstrap articles carousel initialized');
        } else {
          // Fallback: Manual carousel control
          this.startManualArticlesCarousel();
          console.log('Manual articles carousel initialized');
        }
      }
    } catch (error) {
      console.error('Error initializing articles carousel:', error);
      // Fallback to manual carousel
      this.startManualArticlesCarousel();
    }
  }

  startManualArticlesCarousel(): void {
    // Clear any existing interval
    if (this.articlesCarouselInterval) {
      clearInterval(this.articlesCarouselInterval);
    }

    // Start auto-play
    this.articlesCarouselInterval = setInterval(() => {
      this.nextArticleSlide();
    }, 6000);
  }

  nextArticleSlide(): void {
    const totalSlides = 3; // We have 3 slides
    this.currentArticleSlideIndex = (this.currentArticleSlideIndex + 1) % totalSlides;
    this.updateArticlesCarouselDisplay();
  }

  prevArticleSlide(): void {
    const totalSlides = 3; // We have 3 slides
    this.currentArticleSlideIndex = this.currentArticleSlideIndex === 0 ? totalSlides - 1 : this.currentArticleSlideIndex - 1;
    this.updateArticlesCarouselDisplay();
  }

  updateArticlesCarouselDisplay(): void {
    const carouselItems = document.querySelectorAll('#articlesCarousel .carousel-item');
    const indicators = document.querySelectorAll('#articlesCarousel .carousel-indicators button');

    carouselItems.forEach((item, index) => {
      if (index === this.currentArticleSlideIndex) {
        item.classList.add('active');
      } else {
        item.classList.remove('active');
      }
    });

    indicators.forEach((indicator, index) => {
      if (index === this.currentArticleSlideIndex) {
        indicator.classList.add('active');
      } else {
        indicator.classList.remove('active');
      }
    });
  }

  onSubscribeClick() {
    console.log('Subscribe button clicked');
    // Add newsletter subscription logic here
    // You can show a modal or navigate to subscription page
  }

  onPropertyClick(property: any): void {
    console.log('Property clicked:', property);

    this.router.navigate(['developer/projects/models/units/details'], {
      queryParams: {
        unitId: property.id
      }
    });
  }

  scrollToSection() {
    const element = document.getElementById('properties-section' );
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  }
  scrollToFooter() {
  const footerElement = document.getElementById('footer-section');
  if (footerElement) {
    footerElement.scrollIntoView({ behavior: 'smooth' });
  }
}

  // Function to get random area image
  getRandomAreaImage(): string {
    const imageNumbers = [1, 2, 3, 4, 5, 6, 7, 8, 9];
    const randomIndex = Math.floor(Math.random() * imageNumbers.length);
    return `assets/media/home/<USER>/${imageNumbers[randomIndex]}.jpg`;
  }

  // ترجمة عمليات العقار
  getTranslatedOperation(operation: string): string {
    const operationMap: { [key: string]: string } = {
      'sell': 'SELL',
      'purchasing': 'PURCHASING',
      'rent in': 'RENT_IN',
      'rent out': 'RENT_OUT'
    };

    const translationKey = operationMap[operation?.toLowerCase()] || 'SELL';
    return this.translateService.instant(`HOME.PROPERTY_OPERATIONS.${translationKey}`);
  }

  // الحصول على اسم المنطقة حسب اللغة
  getAreaName(property: any): string {
    if (this.currentLang === 'en') {
      return (property.area?.name_en || property.city?.name_en || property.area?.name_ar || property.city?.name_ar || '').slice(0, 20);
    } else {
      return (property.area?.name_ar || property.city?.name_ar || property.area?.name_en || property.city?.name_en || '').slice(0, 20);
    }
  }

  // تنسيق السعر حسب اللغة
  getFormattedPrice(property: any): string {
    const price = property.totalPriceInCash || property.totalPriceInInstallment || 0;
    const currency = this.translateService.instant('HOME.CURRENCY.EGP');

    if (this.currentLang === 'ar') {
      // تحويل الأرقام إلى العربية
      const arabicPrice = this.convertToArabicNumbers(price.toLocaleString());
      return `${arabicPrice} ${currency}`;
    } else {
      return `${price.toLocaleString()} ${currency}`;
    }
  }

  // تحويل الأرقام إلى العربية
  convertToArabicNumbers(englishNumbers: string): string {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    const englishNumbersArray = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

    let result = englishNumbers;
    for (let i = 0; i < englishNumbersArray.length; i++) {
      result = result.replace(new RegExp(englishNumbersArray[i], 'g'), arabicNumbers[i]);
    }
    return result;
  }

  // تنسيق المساحة حسب اللغة
  getFormattedArea(area: number): string {
    const unit = this.translateService.instant('HOME.UNITS.SQM');

    if (this.currentLang === 'ar') {
      const arabicArea = this.convertToArabicNumbers(area.toString());
      return `${arabicArea} ${unit}`;
    } else {
      return `${area} ${unit}`;
    }
  }

  // Helper function for localized area names
  getLocalizedAreaName(area: any): string {
    if (!area) return '';

    if (this.currentLang === 'ar') {
      return area.ar || area.name_ar || area.en || area.name_en || '';
    } else {
      return area.en || area.name_en || area.ar || area.name_ar || '';
    }
  }

  // ترجمة نوع العقار
  getTranslatedPropertyType(type: string): string {
    console.log('Property type:', type, 'Current lang:', this.currentLang);

    const translation = this.propertyTranslationService.translatePropertyType(
      type,
      this.currentLang as 'en' | 'ar',
      20
    );

    console.log('Normalized type:', type.trim().toLowerCase(), 'Translation:', translation);

    return translation;
  }

  // ترجمة نوع الكمبوند
  getTranslatedCompoundType(type: string): string {
    return this.propertyTranslationService.translateCompoundType(
      type,
      this.currentLang as 'en' | 'ar',
      20
    );
  }

  // Navigate to requests page
  navigateToRequests(): void {
    this.closeUserDropdown();
    this.router.navigate(['/requests/sent']);
  }

  // Navigate to profile page
  navigateToProfile(): void {
    this.closeUserDropdown();
    this.router.navigate(['/profile']);
  }

  // Navigate to chat/messages page
  navigateToChat(): void {
    this.closeUserDropdown();
    this.router.navigate(['/chat']);
  }

  // Navigate to stepper modal (new request)
  navigateToStepperModal(): void {
    this.closeUserDropdown();
    this.router.navigate(['/broker/stepper-modal']);
  }

  // Navigate to dashboard
  navigateToDashboard(): void {
    this.closeUserDropdown();

    if(this.isDeveloper()){
      console.log('Navigating to developer dashboard...');
      console.log('Current user:', this.currentUser);

      // Try dashboard first, fallback to projects if permission denied
      this.router.navigate(['/developer/dashboards']).catch(error => {
        console.error('Dashboard navigation failed:', error);
        console.log('Falling back to projects page...');
        this.router.navigate(['/developer/projects']).catch(fallbackError => {
          console.error('Projects navigation also failed:', fallbackError);
          // Last resort - go to developer root
          this.router.navigate(['/developer']);
        });
      });
    } else if (this.isBroker()){
      console.log('Navigating to broker dashboard...');
      this.router.navigate(['/broker/dashboard']).catch(error => {
        console.error('Broker dashboard navigation error:', error);
        // Fallback to broker root
        this.router.navigate(['/broker']);
      });
    } else {
      console.log('User role not recognized, redirecting to home');
      this.router.navigate(['/home']);
    }
  }

  // Get user role for template
  get userRole(): string {
    return this.getUserRole();
  }

  // Handle request button clicks
  handleRequestClick(requestType: string): void {
    const userRole = this.getUserRole();

    // If user is logged in as Client, navigate to stepper modal
    if (userRole === 'client') {
      this.router.navigate(['/broker/stepper-modal']);
      return;
    }

    // If user is Guest or not logged in, show login prompt
    if (!userRole || userRole === 'guest') {
      this.showLoginPrompt();
      return;
    }
  }

  // Show login prompt with SweetAlert
  private showLoginPrompt(): void {
    const isArabic = this.currentLang === 'ar';

    Swal.fire({
      title: isArabic ? 'تسجيل الدخول مطلوب' : 'Login Required',
      text: isArabic ? 'من فضلك سجل دخول أولاً' : 'Please login first',
      icon: 'info',
      showCancelButton: true,
      confirmButtonText: isArabic ? 'تسجيل الدخول' : 'Login',
      cancelButtonText: isArabic ? 'إلغاء' : 'Cancel',
      confirmButtonColor: '#27ae60',
      cancelButtonColor: '#95a5a6',
      reverseButtons: isArabic, // Reverse button order for Arabic
    }).then((result) => {
      if (result.isConfirmed) {
        this.router.navigate(['/authentication/login']);
      }
    });
  }

}
