<div class="mb-20 mt-0">
  <app-developer-header [title]="getTranslatedText('UPDATE_PROJECT')"
    [subtitle]="getTranslatedText('EDIT_AND_UPDATE_EXISTING_PROJECT')">
  </app-developer-header>
</div>

<div class="card rounded-4" [class.rtl-layout]="translationService.getCurrentLanguage() === 'ar'">
  <div class="card-body p-10">
    <div class="stepper stepper-pills d-flex flex-column" id="update_property_stepper">
      <!-- Update Mode Notification -->
      <div class="alert alert-warning text-center mb-4"
        [class.rtl-alert]="translationService.getCurrentLanguage() === 'ar'">
        <i class="fas fa-edit" [class.me-2]="translationService.getCurrentLanguage() !== 'ar'"
          [class.ms-2]="translationService.getCurrentLanguage() === 'ar'"></i>
        <strong>{{ getTranslatedText('EDIT_MODE') }} :</strong>
        {{ getTranslatedText('YOU_ARE_IN_EDIT_MODE') }}
      </div>

      <!-- Header and Progress Bar -->
      <div class="mb-5 text-center">
        <ng-container *ngIf="currentStep === 1">
          <h2 [class.rtl-header]="translationService.getCurrentLanguage() === 'ar'">
            <span class="text-dark-blue fw-bold">{{ getTranslatedText('UPDATE_PROJECT') }} - </span>
            <span class="text-dark-blue fw-normal">{{ getTranslatedText('BASIC_DATA') }}</span>
          </h2>
        </ng-container>
        <ng-container *ngIf="currentStep === 2">
          <h2 [class.rtl-header]="translationService.getCurrentLanguage() === 'ar'">
            <span class="text-dark-blue fw-bold">{{ getTranslatedText('UPDATE_PROJECT') }} - </span>
            <span class="text-dark-blue fw-normal">{{ getTranslatedText('LOCATION') }}</span>
          </h2>
        </ng-container>
        <ng-container *ngIf="currentStep === 3">
          <h2 [class.rtl-header]="translationService.getCurrentLanguage() === 'ar'">
            <span class="text-dark-blue fw-bold">{{ getTranslatedText('UPDATE_PROJECT') }} - </span>
            <span class="text-dark-blue fw-normal">{{ getTranslatedText('PROJECT_TYPE') }}</span>
          </h2>
        </ng-container>
        <ng-container *ngIf="currentStep === 4">
          <h2 [class.rtl-header]="translationService.getCurrentLanguage() === 'ar'">
            <span class="text-dark-blue fw-bold">{{ getTranslatedText('UPDATE_PROJECT') }} - </span>
            <span class="text-dark-blue fw-normal">{{ getTranslatedText('PROJECT_DOCUMENTS') }}</span>
          </h2>
        </ng-container>

        <div class="d-flex justify-content-center align-items-center mb-2">
          <span class="text-success fw-bold">Step {{ currentStep }}</span>
          <span class="text-muted mx-1">of</span>
          <span class="text-muted">{{ totalSteps }}</span>
        </div>

        <div *ngIf="currentStep > 1" class="text-primary cursor-pointer mb-2" (click)="prevStep()">
          Back to previous step
        </div>

        <div class="progress h-8px bg-light-success w-75 mx-auto">
          <div class="progress-bar bg-success" role="progressbar" [style.width]="(currentStep / totalSteps) * 100 + '%'"
            aria-valuenow="50" aria-valuemin="0" aria-valuemax="100"></div>
        </div>
      </div>

      <!-- Form Content -->
      <form class="mx-auto w-100 pt-5 pb-10">
        <!-- Step 1: Basic Property Settings -->
        <div *ngIf="currentStep === 1" [formGroup]="step1Form">
          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block">Project name
            </label>
            <input type="text" class="form-control text-start" [class.is-invalid]="hasFieldError('name')"
              formControlName="name" />
            <div *ngIf="hasFieldError('name')" class="invalid-feedback d-block">
              {{ getFieldErrorMessage("name") }}
            </div>
          </div>

          <div class="mb-10">
            <label class="form-label fw-bold d-block"
              [class.text-start]="translationService.getCurrentLanguage() !== 'ar'"
              [class.text-end]="translationService.getCurrentLanguage() === 'ar'">
              {{ getTranslatedText('DESIGNER') }}
            </label>
            <input type="text" class="form-control"
              [class.text-start]="translationService.getCurrentLanguage() !== 'ar'"
              [class.text-end]="translationService.getCurrentLanguage() === 'ar'"
              [class.is-invalid]="hasFieldError('designer')" formControlName="designer" />
            <div *ngIf="hasFieldError('designer')" class="invalid-feedback d-block">
              {{ getFieldErrorMessage("designer") }}
            </div>
          </div>
          <div class="mb-10">
            <label class="form-label fw-bold d-block"
              [class.text-start]="translationService.getCurrentLanguage() !== 'ar'"
              [class.text-end]="translationService.getCurrentLanguage() === 'ar'">
              {{ getTranslatedText('PROJECT_EXECUTOR') }}
            </label>
            <input type="text" class="form-control"
              [class.text-start]="translationService.getCurrentLanguage() !== 'ar'"
              [class.text-end]="translationService.getCurrentLanguage() === 'ar'"
              [class.is-invalid]="hasFieldError('projectExecutor')" formControlName="projectExecutor" />
            <div *ngIf="hasFieldError('projectExecutor')" class="invalid-feedback d-block">
              {{ getFieldErrorMessage("projectExecutor") }}
            </div>
          </div>
          <div class="mb-10">
            <label class="form-label fw-bold d-block"
              [class.text-start]="translationService.getCurrentLanguage() !== 'ar'"
              [class.text-end]="translationService.getCurrentLanguage() === 'ar'">
              Project management
            </label>
            <input type="text" class="form-control"
              [class.text-start]="translationService.getCurrentLanguage() !== 'ar'"
              [class.text-end]="translationService.getCurrentLanguage() === 'ar'"
              [class.is-invalid]="hasFieldError('managementTeam')" formControlName="managementTeam" />
            <div *ngIf="hasFieldError('managementTeam')" class="invalid-feedback d-block">
              {{ getFieldErrorMessage("managementTeam") }}
            </div>
          </div>
        </div>

        <!-- Step 2: Location Information -->
        <div *ngIf="currentStep === 2" [formGroup]="step2Form"
          [class.rtl-form]="translationService.getCurrentLanguage() === 'ar'">
          <div class="mb-10">
            <label class="form-label fw-bold d-block"
              [class.text-start]="translationService.getCurrentLanguage() !== 'ar'"
              [class.text-end]="translationService.getCurrentLanguage() === 'ar'">{{ getTranslatedText('CITY')
              }}</label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 d-flex justify-content-between align-items-center"
                [class.text-start]="translationService.getCurrentLanguage() !== 'ar'"
                [class.text-end]="translationService.getCurrentLanguage() === 'ar'" type="button" id="cityDropdown"
                data-bs-toggle="dropdown" aria-expanded="false">
                <span>{{ selectedCityName || getTranslatedText('SELECT_CITY') }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul class="dropdown-menu w-100" aria-labelledby="cityDropdown"
                style="max-height: 300px; overflow-y: auto">
                <li *ngFor="let city of cities">
                  <a class="dropdown-item" [class.text-start]="translationService.getCurrentLanguage() !== 'ar'"
                    [class.text-end]="translationService.getCurrentLanguage() === 'ar'"
                    (click)="selectCity(city.id, city.name_en || city.name)">
                    {{ city.name_en || city.name }}
                  </a>
                </li>
              </ul>
            </div>
          </div>

          <div class="mb-10">
            <label class="form-label fw-bold d-block"
              [class.text-start]="translationService.getCurrentLanguage() !== 'ar'"
              [class.text-end]="translationService.getCurrentLanguage() === 'ar'">{{ getTranslatedText('AREA')
              }}</label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 d-flex justify-content-between align-items-center"
                [class.text-start]="translationService.getCurrentLanguage() !== 'ar'"
                [class.text-end]="translationService.getCurrentLanguage() === 'ar'" type="button" id="areaDropdown"
                data-bs-toggle="dropdown" aria-expanded="false">
                <span>{{ selectedAreaName || getTranslatedText('SELECT_AREA') }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul class="dropdown-menu w-100" aria-labelledby="areaDropdown"
                style="max-height: 300px; overflow-y: auto">
                <li *ngFor="let area of areas">
                  <a class="dropdown-item" [class.text-start]="translationService.getCurrentLanguage() !== 'ar'"
                    [class.text-end]="translationService.getCurrentLanguage() === 'ar'"
                    (click)="selectArea(area.id, area.name_en || area.name)">
                    {{ area.name_en || area.name }}
                  </a>
                </li>
              </ul>
            </div>
          </div>

          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block">
              Details address</label>
            <input type="text" class="form-control text-start" [class.is-invalid]="hasFieldError('address')"
              formControlName="address" placeholder=" Enter the address in details" />
            <div *ngIf="hasFieldError('address')" class="invalid-feedback d-block">
              {{ getFieldErrorMessage("address") }}
            </div>
          </div>

          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block">Website link on Google Maps</label>
            <input type="text" class="form-control text-start" [class.is-invalid]="hasFieldError('googleMapsLink')"
              formControlName="googleMapsLink" placeholder=" Enter the map link" />
            <div *ngIf="hasFieldError('googleMapsLink')" class="invalid-feedback d-block">
              {{ getFieldErrorMessage("googleMapsLink") }}
            </div>
          </div>

          <!-- Hidden fields -->
          <input type="hidden" formControlName="cityId" />
          <input type="hidden" formControlName="areaId" />
          <input type="hidden" formControlName="googleMapUrl" />
        </div>

        <!-- Step 3: Project type   -->
        <div *ngIf="currentStep === 3" [formGroup]="step3Form">
          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block">
              Project type
            </label>
            <div class="dropdown">
              <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center"
                type="button" id="projectTypeDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <span>{{
                  step3Form.get("projectType")?.value || "commercial..."
                  }}</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <ul class="dropdown-menu w-100" aria-labelledby="projectTypeDropdown"
                style="max-height: 300px; overflow-y: auto">
                <li>
                  <a class="dropdown-item text-start" (click)="selectProjectType(' residential')">
                    residential</a>
                </li>
                <li>
                  <a class="dropdown-item text-start" (click)="selectProjectType('commercial')">commercial</a>
                </li>
                <li>
                  <a class="dropdown-item text-start" (click)="selectProjectType('mixed')">mixed</a>
                </li>
              </ul>
            </div>
          </div>

          <div class="mb-10">
            <label class="form-label fw-bold text-start d-block">
              Number of project units</label>

            <!-- Buildings Row -->
            <div class="d-flex mb-3 align-items-center">
              <button type="button" class="btn btn-primary me-3" style="width: 150px; background-color: #1e1e7c">
                Buildings
              </button>
              <div class="flex-grow-1">
                <input type="number" class="form-control text-start"
                  [class.is-invalid]="hasFieldError('buildingsCount')" formControlName="buildingsCount"
                  placeholder="00" />
                <div *ngIf="hasFieldError('buildingsCount')" class="invalid-feedback d-block">
                  {{ getFieldErrorMessage("buildingsCount") }}
                </div>
              </div>
            </div>

            <!-- Apartments Row -->
            <div class="d-flex mb-3 align-items-center">
              <button type="button" class="btn btn-primary me-3" style="width: 150px; background-color: #1e1e7c">
                Apartments
              </button>
              <div class="flex-grow-1">
                <input type="number" class="form-control text-start"
                  [class.is-invalid]="hasFieldError('apartmentsCount')" formControlName="apartmentsCount"
                  placeholder="00" />
                <div *ngIf="hasFieldError('apartmentsCount')" class="invalid-feedback d-block">
                  {{ getFieldErrorMessage("apartmentsCount") }}
                </div>
              </div>
            </div>

            <!-- Villas Row -->
            <div class="d-flex mb-3 align-items-center">
              <button type="button" class="btn btn-primary me-3" style="width: 150px; background-color: #1e1e7c">
                Villas
              </button>
              <div class="flex-grow-1">
                <input type="number" class="form-control text-start" [class.is-invalid]="hasFieldError('villasCount')"
                  formControlName="villasCount" placeholder="00" />
                <div *ngIf="hasFieldError('villasCount')" class="invalid-feedback d-block">
                  {{ getFieldErrorMessage("villasCount") }}
                </div>
              </div>
            </div>

            <!-- Duplexes Row -->
            <div class="d-flex mb-3 align-items-center">
              <button type="button" class="btn btn-primary me-3" style="width: 150px; background-color: #1e1e7c">
                Duplexes
              </button>
              <div class="flex-grow-1">
                <input type="number" class="form-control text-start" [class.is-invalid]="hasFieldError('duplexCount')"
                  formControlName="duplexCount" placeholder="00" />
                <div *ngIf="hasFieldError('duplexCount')" class="invalid-feedback d-block">
                  {{ getFieldErrorMessage("duplexCount") }}
                </div>
              </div>
            </div>

            <!-- administrative units count Row -->
            <div class="d-flex mb-3 align-items-center">
              <button type="button" class="btn btn-primary me-3" style="width: 150px; background-color: #1e1e7c">
                Administrative Units
              </button>
              <div class="flex-grow-1">
                <input type="number" class="form-control text-start"
                  [class.is-invalid]="hasFieldError('administrativeUnitsCount')"
                  formControlName="administrativeUnitsCount" placeholder="00" />
                <div *ngIf="hasFieldError('administrativeUnitsCount')" class="invalid-feedback d-block">
                  {{ getFieldErrorMessage("administrativeUnitsCount") }}
                </div>
              </div>
            </div>

            <!-- commercial units count Row-->
            <div class="d-flex mb-3 align-items-center">
              <button type="button" class="btn btn-primary me-3" style="width: 150px; background-color: #1e1e7c">
                Commercial Units
              </button>
              <div class="flex-grow-1">
                <input type="number" class="form-control text-start"
                  [class.is-invalid]="hasFieldError('commercialUnitsCount')" formControlName="commercialUnitsCount"
                  placeholder="00" />
                <div *ngIf="hasFieldError('commercialUnitsCount')" class="invalid-feedback d-block">
                  {{ getFieldErrorMessage("commercialUnitsCount") }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Step 4: Project Documents -->
        <div *ngIf="currentStep === 4" [formGroup]="step4Form">
          <!-- Project Documents Cards -->
          <div class="mb-10 upload-card-container">
            <!-- Project Logo -->
            <div class="card mb-5 cursor-pointer">
              <label for="projectLogo" class="card-body text-center py-3">
                <div class="upload-icon">
                  <i class="fas fa-arrow-up"></i>
                </div>
                <span class="upload-text">
                  upload project logo
                  <!-- Show existing files count -->
                  <span *ngIf="existingFiles.logoImage" class="badge bg-info ms-2" title="Existing files">
                    1 existing
                  </span>
                  <!-- Show new files count -->
                  <span *ngIf="getFileCount('logoImage') > 0" class="badge bg-success ms-2" title="New files selected">
                    {{ getFileCount("logoImage") }} new
                  </span>
                </span>
                <input type="file" id="projectLogo" class="d-none" (change)="onFileChange($event, 'logoImage')"
                  accept="image/*" />
              </label>

              <!-- Simple existing logo display -->
              <div *ngIf="existingFiles?.logoImage" class="mt-3">
                <p class="text-muted mb-2">Current Logo:</p>
                <div class="position-relative d-inline-block">
                  <img [src]="existingFiles.logoImage" class="img-fluid rounded"
                    style="height: 100px; width: auto; max-width: 200px" alt="Current Logo" />
                  <button type="button" class="btn btn-danger btn-sm position-absolute top-0 end-0 m-1"
                    (click)="removeImage('logoImage')" title="Delete">
                    <i class="fas fa-times"></i>
                  </button>
                </div>
              </div>
            </div>

            <!-- Project Layout -->
            <div class="card mb-5 cursor-pointer">
              <label for="projectLayout" class="card-body text-center py-3">
                <div class="upload-icon">
                  <i class="fas fa-arrow-up"></i>
                </div>
                <span class="upload-text">
                  upload project layout
                  <!-- Show existing files count -->
                  <span *ngIf="existingFiles.coverImage" class="badge bg-info ms-2" title="Existing files">
                    1 existing
                  </span>
                  <!-- Show new files count -->
                  <span *ngIf="getFileCount('coverImage') > 0" class="badge bg-success ms-2" title="New files selected">
                    {{ getFileCount("coverImage") }} new
                  </span>
                </span>
                <input type="file" id="projectLayout" class="d-none" (change)="onFileChange($event, 'coverImage')"
                  accept="image/*" />
              </label>

              <!-- Simple existing cover image display -->
              <div *ngIf="existingFiles.coverImage" class="mt-3">
                <p class="text-muted mb-2">Current Cover Image:</p>
                <div class="position-relative d-inline-block">
                  <img [src]="existingFiles.coverImage" class="img-fluid rounded"
                    style="height: 100px; width: auto; max-width: 200px" alt="Current Cover Image" />
                  <button type="button" class="btn btn-danger btn-sm position-absolute top-0 end-0 m-1"
                    (click)="removeImage('coverImage')" title="Delete">
                    <i class="fas fa-times"></i>
                  </button>
                </div>
              </div>
            </div>

            <!-- Project Licenses -->
            <div class="card mb-5 cursor-pointer">
              <label for="projectLicenses" class="card-body text-center py-3">
                <div class="upload-icon">
                  <i class="fas fa-arrow-up"></i>
                </div>
                <span class="upload-text">
                  upload project licenses
                  <!-- Show existing files count -->
                  <span *ngIf="existingFiles.masterPlan" class="badge bg-info ms-2" title="Existing files">
                    1 existing
                  </span>
                  <!-- Show new files count -->
                  <span *ngIf="getFileCount('masterPlan') > 0" class="badge bg-success ms-2" title="New files selected">
                    {{ getFileCount("masterPlan") }} new
                  </span>
                </span>
                <input type="file" id="projectLicenses" class="d-none" (change)="onFileChange($event, 'masterPlan')"
                  accept="image/*" />
              </label>

              <!-- Simple existing master plan display -->
              <div *ngIf="existingFiles.masterPlan" class="mt-3">
                <p class="text-muted mb-2">Current Master Plan:</p>
                <div class="position-relative d-inline-block">
                  <img [src]="existingFiles.masterPlan" class="img-fluid rounded"
                    style="height: 100px; width: auto; max-width: 200px" alt="Current Master Plan" />
                  <button type="button" class="btn btn-danger btn-sm position-absolute top-0 end-0 m-1"
                    (click)="removeImage('masterPlan')" title="Delete">
                    <i class="fas fa-times"></i>
                  </button>
                </div>
              </div>
            </div>

            <!-- Project Images -->
            <div class="card mb-5 cursor-pointer">
              <label for="projectImages" class="card-body text-center py-3">
                <div class="upload-icon">
                  <i class="fas fa-arrow-up"></i>
                </div>
                <span class="upload-text">
                  upload project images
                  <!-- Show existing files count -->
                  <span *ngIf="existingFiles.galleryImages?.length > 0" class="badge bg-info ms-2"
                    title="Existing files">
                    {{ existingFiles.galleryImages.length }} existing
                  </span>
                  <!-- Show new files count -->
                  <span *ngIf="getFileCount('gallery') > 0" class="badge bg-success ms-2" title="New files selected">
                    {{ getFileCount("gallery") }} new
                  </span>
                </span>
                <input type="file" id="projectImages" class="d-none" (change)="onFileChange($event, 'gallery')"
                  accept="image/*" multiple />
              </label>

              <!-- Simple existing gallery images display -->
              <div *ngIf="existingFiles.galleryImages?.length > 0" class="mt-3">
                <p class="text-muted mb-2">Current Gallery Images:</p>
                <div class="row g-2">
                  <div *ngFor="
                      let image of existingFiles.galleryImages;
                      let i = index
                    " class="col-6 col-md-4 col-lg-3">
                    <div class="position-relative">
                      <img [src]="image.url" class="img-fluid rounded"
                        style="height: 80px; width: 100%; object-fit: cover" [alt]="'Gallery Image ' + (i + 1)" />
                      <button type="button" class="btn btn-danger btn-sm position-absolute top-0 end-0 m-1"
                        (click)="removeGalleryItem('image', i)" title="Delete">
                        <i class="fas fa-times"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Project Videos -->
            <div class="card mb-5 cursor-pointer">
              <label for="projectVideos" class="card-body text-center py-3">
                <div class="upload-icon">
                  <i class="fas fa-arrow-up"></i>
                </div>
                <span class="upload-text">
                  upload project videos
                  <!-- Show existing files count -->
                  <span *ngIf="existingFiles.galleryVideos?.length > 0" class="badge bg-info ms-2"
                    title="Existing files">
                    {{ existingFiles.galleryVideos.length }} existing
                  </span>
                  <!-- Show new files count -->
                  <span *ngIf="getFileCount('videos') > 0" class="badge bg-success ms-2" title="New files selected">
                    {{ getFileCount("videos") }} new
                  </span>
                </span>
                <input type="file" id="projectVideos" class="d-none" (change)="onFileChange($event, 'videos')"
                  accept="video/*" multiple />
              </label>

              <!-- Simple existing gallery videos display -->
              <div *ngIf="existingFiles.galleryVideos?.length > 0" class="mt-3">
                <p class="text-muted mb-2">Current Gallery Videos:</p>
                <div class="row g-2">
                  <div *ngFor="
                      let video of existingFiles.galleryVideos;
                      let i = index
                    " class="col-6 col-md-4 col-lg-3">
                    <div class="position-relative">
                      <div class="video-thumbnail rounded d-flex align-items-center justify-content-center" style="
                          height: 80px;
                          width: 100%;
                          background-color: #f8f9fa;
                          border: 1px solid #dee2e6;
                        ">
                        <i class="fas fa-play-circle fa-2x text-primary"></i>
                      </div>
                      <button type="button" class="btn btn-danger btn-sm position-absolute top-0 end-0 m-1"
                        (click)="removeGalleryItem('video', i)" title="Delete">
                        <i class="fas fa-times"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Navigation Buttons -->
        <div *ngIf="currentStep === 1" class="d-flex justify-content-between pt-10"
          [class.rtl-buttons]="translationService.getCurrentLanguage() === 'ar'">
          <button type="button" class="btn btn-light-dark btn-lg px-6 py-3" (click)="cancel()">
            {{ getTranslatedText('CANCEL') }}
          </button>

          <button type="button" class="btn btn-lg btn-navy px-10 py-3 rounded-pill" [disabled]="!isCurrentFormValid()"
            (click)="nextStep()">
            <span class="indicator-label text-white">
              {{ getTranslatedText('NEXT') }} - {{ getTranslatedText('LOCATION') }}
            </span>
          </button>
        </div>

        <!-- ******-->
        <div *ngIf="currentStep > 1" class="d-flex justify-content-center pt-10"
          [class.rtl-buttons]="translationService.getCurrentLanguage() === 'ar'">
          <ng-container *ngIf="currentStep !== totalSteps">
            <button type="button" class="btn btn-lg btn-navy px-10 py-3 rounded-pill" [disabled]="!isCurrentFormValid()"
              (click)="nextStep()">
              <span class="indicator-label text-white">
                <ng-container *ngIf="currentStep === 2">
                  {{ getTranslatedText('NEXT') }} - {{ getTranslatedText('PROJECT_TYPE') }}
                </ng-container>
                <ng-container *ngIf="currentStep === 3">
                  {{ getTranslatedText('NEXT') }} - {{ getTranslatedText('PROJECT_DOCUMENTS') }}
                </ng-container>
              </span>
            </button>
          </ng-container>

          <ng-container *ngIf="currentStep === totalSteps">
            <div class="d-flex flex-column align-items-center">
              <button type="button" class="btn btn-lg btn-blue-custom px-10 py-3 rounded-pill mb-3 w-100"
                [disabled]="!isCurrentFormValid()" (click)="submitForm()">
                <span class="indicator-label text-white">{{ getTranslatedText('UPDATE') }} {{
                  getTranslatedText('UPDATE_PROJECT') }}</span>
              </button>
            </div>
          </ng-container>
        </div>
      </form>
    </div>
  </div>
</div>
