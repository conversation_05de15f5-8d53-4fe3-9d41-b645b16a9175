<div class="filter-dropdown" [class.rtl-filter]="translationService.getCurrentLanguage() === 'ar'">
  <div class="mb-2">
    <label class="form-label">{{ getTranslatedText('AREA') }}:</label>
    <select class="form-control form-control-sm" [(ngModel)]="filter.finishingType">
      <option value="">{{ getTranslatedText('SELECT') }}</option>
      <option *ngFor="let area of areas" [value]="area.id">{{ getAreaName(area) }}</option>
    </select>
  </div>

  <div class="mb-2">
    <label class="form-label">{{ getTranslatedText('UNIT_AREA') }}:</label>
    <input type="text" class="form-control form-control-sm" [placeholder]="getTranslatedText('ENTER_UNIT_AREA')"
      [(ngModel)]="filter.unitArea" />
  </div>

  <div class="mb-2">
    <label class="form-label">{{ getTranslatedText('VIEW') }}:</label>
    <select class="form-control form-control-sm" [(ngModel)]="filter.finishingType">
      <option value="">{{ getTranslatedText('SELECT') }}</option>
      <option *ngFor="let view of views" [value]="view.value">{{ getViewName(view) }}</option>
    </select>
  </div>

  <div class="mb-2">
    <label class="form-label">{{ getTranslatedText('PRICE') }}:</label>
    <input type="number" class="form-control form-control-sm" [placeholder]="getTranslatedText('ENTER_PRICE')"
      [(ngModel)]="filter.price" />
  </div>

  <!-- <div class="mb-2">
    <label class="form-label">Finishing Status:</label>
    <select class="form-control form-control-sm" [(ngModel)]="filter.finishingType">
      <option value="">select</option>
      <option *ngFor="let type of finishingTypes" [value]="type.value">{{ type.key }}</option>
    </select>
  </div>

  <div class="mb-2">
    <label class="form-label">Status:</label>
    <select class="form-control form-control-sm" [(ngModel)]="filter.status">
      <option value="">Select</option>
      <option *ngFor="let state of status" [value]="state.value">{{ state.key }}</option>
    </select>
  </div> -->

  <div class="mb-2">
    <label class="form-label">{{ getTranslatedText('UNIT_TYPE') }}:</label>
    <select class="form-control form-control-sm" [(ngModel)]="filter.unitType">
      <option value="">{{ getTranslatedText('SELECT') }}</option>
      <option *ngFor="let unit of unitTypes" [value]="unit.value">{{ unit.key}}</option>
    </select>
  </div>

  <button class="btn btn-sm btn-primary w-100" (click)="apply()">{{ getTranslatedText('APPLY') }}</button>
</div>
